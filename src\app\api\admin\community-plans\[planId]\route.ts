import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { CommunityPlan } from "@/models/CommunityPlan";

// GET /api/admin/community-plans/[planId] - Get specific plan
export async function GET(
  request: NextRequest,
  { params }: { params: { planId: string } }
) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    const plan = await CommunityPlan.findOne({
      _id: params.planId,
      adminId: session.user.id
    });

    if (!plan) {
      return NextResponse.json(
        { error: "Plan not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      plan
    });

  } catch (error: any) {
    console.error("Get plan error:", error);
    return NextResponse.json(
      { error: "Failed to fetch plan" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/community-plans/[planId] - Update plan
export async function PUT(
  request: NextRequest,
  { params }: { params: { planId: string } }
) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    const updateData = await request.json();
    const { communityId, ...planFields } = updateData;

    // Find the plan
    const plan = await CommunityPlan.findOne({
      _id: params.planId,
      adminId: session.user.id
    });

    if (!plan) {
      return NextResponse.json(
        { error: "Plan not found" },
        { status: 404 }
      );
    }

    // If setting as default, unset other default plans
    if (planFields.isDefault && !plan.isDefault) {
      await CommunityPlan.updateMany(
        { 
          communityId: plan.communityId, 
          adminId: session.user.id,
          _id: { $ne: params.planId }
        },
        { isDefault: false }
      );
    }

    // Update the plan
    Object.assign(plan, planFields);
    await plan.save();

    return NextResponse.json({
      success: true,
      message: "Plan updated successfully",
      plan
    });

  } catch (error: any) {
    console.error("Update plan error:", error);
    return NextResponse.json(
      { error: "Failed to update plan" },
      { status: 500 }
    );
  }
}

// PATCH /api/admin/community-plans/[planId] - Partial update (e.g., toggle active)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { planId: string } }
) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    const updateData = await request.json();

    // Find and update the plan
    const plan = await CommunityPlan.findOneAndUpdate(
      {
        _id: params.planId,
        adminId: session.user.id
      },
      updateData,
      { new: true }
    );

    if (!plan) {
      return NextResponse.json(
        { error: "Plan not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Plan updated successfully",
      plan
    });

  } catch (error: any) {
    console.error("Patch plan error:", error);
    return NextResponse.json(
      { error: "Failed to update plan" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/community-plans/[planId] - Delete plan
export async function DELETE(
  request: NextRequest,
  { params }: { params: { planId: string } }
) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Find the plan
    const plan = await CommunityPlan.findOne({
      _id: params.planId,
      adminId: session.user.id
    });

    if (!plan) {
      return NextResponse.json(
        { error: "Plan not found" },
        { status: 404 }
      );
    }

    // Check if plan has active subscribers
    if (plan.totalSubscribers > 0) {
      return NextResponse.json(
        { error: "Cannot delete plan with active subscribers" },
        { status: 400 }
      );
    }

    // Delete the plan
    await CommunityPlan.findByIdAndDelete(params.planId);

    // If this was the default plan, set another plan as default
    if (plan.isDefault) {
      const nextPlan = await CommunityPlan.findOne({
        communityId: plan.communityId,
        adminId: session.user.id,
        isActive: true
      });

      if (nextPlan) {
        nextPlan.isDefault = true;
        await nextPlan.save();
      }
    }

    return NextResponse.json({
      success: true,
      message: "Plan deleted successfully"
    });

  } catch (error: any) {
    console.error("Delete plan error:", error);
    return NextResponse.json(
      { error: "Failed to delete plan" },
      { status: 500 }
    );
  }
}
