import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { CommunityPlan } from "@/models/CommunityPlan";
import { Community } from "@/models/Community";

// GET /api/admin/community-plans - Get all plans for a community
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    const { searchParams } = new URL(request.url);
    const communityId = searchParams.get('communityId');

    if (!communityId) {
      return NextResponse.json(
        { error: "Community ID is required" },
        { status: 400 }
      );
    }

    // Verify admin owns the community
    const community = await Community.findOne({
      _id: communityId,
      adminId: session.user.id
    });

    if (!community) {
      return NextResponse.json(
        { error: "Community not found or access denied" },
        { status: 404 }
      );
    }

    // Get all plans for the community
    const plans = await CommunityPlan.find({
      communityId,
      adminId: session.user.id
    }).sort({ createdAt: -1 });

    return NextResponse.json({
      success: true,
      plans
    });

  } catch (error: any) {
    console.error("Get community plans error:", error);
    return NextResponse.json(
      { error: "Failed to fetch plans" },
      { status: 500 }
    );
  }
}

// POST /api/admin/community-plans - Create a new plan
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    const planData = await request.json();
    const { communityId, ...planFields } = planData;

    if (!communityId) {
      return NextResponse.json(
        { error: "Community ID is required" },
        { status: 400 }
      );
    }

    // Verify admin owns the community
    const community = await Community.findOne({
      _id: communityId,
      adminId: session.user.id
    });

    if (!community) {
      return NextResponse.json(
        { error: "Community not found or access denied" },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!planFields.name || !planFields.amount || planFields.amount <= 0) {
      return NextResponse.json(
        { error: "Plan name and valid amount are required" },
        { status: 400 }
      );
    }

    // If setting as default, unset other default plans
    if (planFields.isDefault) {
      await CommunityPlan.updateMany(
        { communityId, adminId: session.user.id },
        { isDefault: false }
      );
    }

    // Create the plan
    const plan = new CommunityPlan({
      ...planFields,
      adminId: session.user.id,
      communityId,
      totalSubscribers: 0,
      totalRevenue: 0,
      conversionRate: 0
    });

    await plan.save();

    // Create Razorpay plan if needed (for recurring subscriptions)
    if (planFields.interval !== 'one_time') {
      try {
        await createRazorpayPlan(plan);
      } catch (razorpayError) {
        console.error("Razorpay plan creation failed:", razorpayError);
        // Continue without failing the entire operation
      }
    }

    return NextResponse.json({
      success: true,
      message: "Plan created successfully",
      plan
    });

  } catch (error: any) {
    console.error("Create community plan error:", error);
    return NextResponse.json(
      { error: "Failed to create plan" },
      { status: 500 }
    );
  }
}

// Helper function to create Razorpay plan
async function createRazorpayPlan(plan: any) {
  const Razorpay = require('razorpay');
  
  const razorpay = new Razorpay({
    key_id: process.env.RAZORPAY_KEY_ID,
    key_secret: process.env.RAZORPAY_KEY_SECRET,
  });

  const razorpayPlan = await razorpay.plans.create({
    period: plan.interval === 'monthly' ? 'monthly' : 'yearly',
    interval: plan.intervalCount,
    item: {
      name: plan.name,
      amount: plan.amount, // Amount in paise
      currency: plan.currency,
      description: plan.description || `${plan.name} subscription`
    },
    notes: {
      community_id: plan.communityId.toString(),
      admin_id: plan.adminId.toString(),
      plan_id: plan._id.toString()
    }
  });

  // Update plan with Razorpay plan ID
  plan.razorpayPlanId = razorpayPlan.id;
  await plan.save();

  return razorpayPlan;
}
