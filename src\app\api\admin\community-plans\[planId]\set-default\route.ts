import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "@/lib/auth-helpers";
import { dbconnect } from "@/lib/db";
import { CommunityPlan } from "@/models/CommunityPlan";

// POST /api/admin/community-plans/[planId]/set-default - Set plan as default
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ planId: string }> }
) {
  const { planId } = await params;
  try {
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await dbconnect();

    // Find the plan
    const plan = await CommunityPlan.findOne({
      _id: planId,
      adminId: session.user.id
    });

    if (!plan) {
      return NextResponse.json(
        { error: "Plan not found" },
        { status: 404 }
      );
    }

    // Unset all other default plans for this community
    await CommunityPlan.updateMany(
      { 
        communityId: plan.communityId, 
        adminId: session.user.id,
        _id: { $ne: planId }
      },
      { isDefault: false }
    );

    // Set this plan as default
    plan.isDefault = true;
    await plan.save();

    return NextResponse.json({
      success: true,
      message: "Plan set as default successfully",
      plan
    });

  } catch (error: any) {
    console.error("Set default plan error:", error);
    return NextResponse.json(
      { error: "Failed to set default plan" },
      { status: 500 }
    );
  }
}
